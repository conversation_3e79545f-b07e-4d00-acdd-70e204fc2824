#!/usr/bin/env node

/**
 * Simple API test script for MasChain Energy Trading Platform
 * Run with: node test-api.js
 */

const http = require('http')

const BASE_URL = 'http://localhost:3001'

// Helper function to make HTTP requests
function makeRequest(options, data = null) {
  return new Promise((resolve, reject) => {
    const req = http.request(options, (res) => {
      let body = ''
      res.on('data', (chunk) => {
        body += chunk
      })
      res.on('end', () => {
        try {
          const result = {
            statusCode: res.statusCode,
            headers: res.headers,
            body: body ? JSON.parse(body) : null
          }
          resolve(result)
        } catch (error) {
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            body: body
          })
        }
      })
    })

    req.on('error', (error) => {
      reject(error)
    })

    if (data) {
      req.write(JSON.stringify(data))
    }

    req.end()
  })
}

// Test functions
async function testHealthCheck() {
  console.log('\n🔍 Testing Health Check...')
  try {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/health',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    }

    const response = await makeRequest(options)
    console.log(`✅ Health Check: ${response.statusCode}`)
    console.log(`   Status: ${response.body?.status}`)
    console.log(`   Version: ${response.body?.version}`)
    return response.statusCode === 200
  } catch (error) {
    console.log(`❌ Health Check Failed: ${error.message}`)
    return false
  }
}

async function testAuthChallenge() {
  console.log('\n🔍 Testing Auth Challenge...')
  try {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/auth/challenge',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    }

    const data = {
      walletAddress: 'test_wallet_address_123'
    }

    const response = await makeRequest(options, data)
    console.log(`✅ Auth Challenge: ${response.statusCode}`)
    if (response.body?.success) {
      console.log(`   Nonce: ${response.body.data.nonce}`)
      return response.body.data.nonce
    }
    return null
  } catch (error) {
    console.log(`❌ Auth Challenge Failed: ${error.message}`)
    return null
  }
}

async function testWalletLogin(nonce) {
  console.log('\n🔍 Testing Wallet Login...')
  try {
    const walletAddress = 'test_wallet_address_123'
    const timestamp = Date.now()
    const signature = `demo_signature_${Buffer.from(`${walletAddress}:${timestamp}:${nonce}`).toString('base64')}`

    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/auth/wallet-login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Wallet-Address': walletAddress,
        'X-Wallet-Signature': signature,
        'X-Timestamp': timestamp.toString(),
        'X-Nonce': nonce
      }
    }

    const data = {
      walletAddress,
      signature,
      timestamp,
      nonce
    }

    const response = await makeRequest(options, data)
    console.log(`✅ Wallet Login: ${response.statusCode}`)
    if (response.body?.success) {
      console.log(`   Token received: ${response.body.data.token.substring(0, 20)}...`)
      return response.body.data.token
    }
    return null
  } catch (error) {
    console.log(`❌ Wallet Login Failed: ${error.message}`)
    return null
  }
}

async function testMarketOffers() {
  console.log('\n🔍 Testing Market Offers...')
  try {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/market/offers?limit=5',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    }

    const response = await makeRequest(options)
    console.log(`✅ Market Offers: ${response.statusCode}`)
    if (response.body?.success) {
      console.log(`   Offers count: ${response.body.data.offers?.length || 0}`)
      console.log(`   Total: ${response.body.data.total || 0}`)
    }
    return response.statusCode === 200
  } catch (error) {
    console.log(`❌ Market Offers Failed: ${error.message}`)
    return false
  }
}

async function testMarketStats() {
  console.log('\n🔍 Testing Market Stats...')
  try {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/market/stats',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    }

    const response = await makeRequest(options)
    console.log(`✅ Market Stats: ${response.statusCode}`)
    if (response.body?.success) {
      const stats = response.body.data
      console.log(`   Total Offers: ${stats.totalOffers}`)
      console.log(`   Volume Traded: ${stats.totalVolumeTraded}`)
      console.log(`   Average Price: ${stats.averagePrice}`)
    }
    return response.statusCode === 200
  } catch (error) {
    console.log(`❌ Market Stats Failed: ${error.message}`)
    return false
  }
}

async function testEnergyStats(token) {
  console.log('\n🔍 Testing Energy Stats (Authenticated)...')
  try {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/api/energy/stats',
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
      }
    }

    const response = await makeRequest(options)
    console.log(`✅ Energy Stats: ${response.statusCode}`)
    if (response.body?.success) {
      const stats = response.body.data
      console.log(`   Total Production: ${stats.totalProduction}`)
      console.log(`   Total Consumption: ${stats.totalConsumption}`)
      console.log(`   Current Balance: ${stats.currentBalance}`)
    }
    return response.statusCode === 200
  } catch (error) {
    console.log(`❌ Energy Stats Failed: ${error.message}`)
    return false
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Starting MasChain Energy Trading Platform API Tests')
  console.log('=' .repeat(60))

  let passedTests = 0
  let totalTests = 0

  // Test 1: Health Check
  totalTests++
  if (await testHealthCheck()) {
    passedTests++
  }

  // Test 2: Auth Challenge
  totalTests++
  const nonce = await testAuthChallenge()
  if (nonce) {
    passedTests++
  }

  // Test 3: Wallet Login
  totalTests++
  let token = null
  if (nonce) {
    token = await testWalletLogin(nonce)
    if (token) {
      passedTests++
    }
  }

  // Test 4: Market Offers
  totalTests++
  if (await testMarketOffers()) {
    passedTests++
  }

  // Test 5: Market Stats
  totalTests++
  if (await testMarketStats()) {
    passedTests++
  }

  // Test 6: Energy Stats (requires authentication)
  totalTests++
  if (token && await testEnergyStats(token)) {
    passedTests++
  }

  // Summary
  console.log('\n' + '=' .repeat(60))
  console.log(`📊 Test Results: ${passedTests}/${totalTests} tests passed`)
  
  if (passedTests === totalTests) {
    console.log('🎉 All tests passed! API is working correctly.')
  } else {
    console.log('⚠️  Some tests failed. Check the logs above for details.')
  }

  console.log('\n💡 Next steps:')
  console.log('   1. Start the frontend: npm run dev')
  console.log('   2. Test the full application in browser')
  console.log('   3. Check WebSocket connections')
  console.log('   4. Test trading functionality')
}

// Check if server is running
async function checkServerRunning() {
  try {
    const options = {
      hostname: 'localhost',
      port: 3001,
      path: '/health',
      method: 'GET',
      timeout: 2000
    }

    await makeRequest(options)
    return true
  } catch (error) {
    return false
  }
}

// Start tests
async function main() {
  const isServerRunning = await checkServerRunning()
  
  if (!isServerRunning) {
    console.log('❌ Server is not running on http://localhost:3001')
    console.log('💡 Start the server first with: npm run backend:dev')
    process.exit(1)
  }

  await runTests()
}

main().catch(console.error)
