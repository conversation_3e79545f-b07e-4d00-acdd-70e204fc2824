import express from 'express'
import { body, query, validationResult } from 'express-validator'
import { MarketService } from '../services/marketService'
import { authenticateWallet } from '../middleware/auth'
import { rateLimiter } from '../middleware/rateLimiter'

const router = express.Router()
const marketService = new MarketService()

// Validation middleware
const validateRequest = (req: express.Request, res: express.Response, next: express.NextFunction) => {
  const errors = validationResult(req)
  if (!errors.isEmpty()) {
    return res.status(400).json({ 
      error: 'Validation failed', 
      details: errors.array() 
    })
  }
  next()
}

/**
 * GET /api/market/offers
 * Get active market offers
 */
router.get('/offers',
  [
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative'),
    query('type').optional().isIn(['immediate', 'scheduled', 'recurring']).withMessage('Invalid offer type'),
    query('minPrice').optional().isFloat({ min: 0 }).withMessage('Min price must be non-negative'),
    query('maxPrice').optional().isFloat({ min: 0 }).withMessage('Max price must be non-negative'),
    query('minAmount').optional().isFloat({ min: 0 }).withMessage('Min amount must be non-negative'),
    query('maxAmount').optional().isFloat({ min: 0 }).withMessage('Max amount must be non-negative')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { 
        limit = 50, 
        offset = 0, 
        type, 
        minPrice, 
        maxPrice, 
        minAmount, 
        maxAmount 
      } = req.query

      const offers = await marketService.getActiveOffers({
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        type: type as 'immediate' | 'scheduled' | 'recurring' | undefined,
        priceRange: minPrice || maxPrice ? {
          min: minPrice ? parseFloat(minPrice as string) : undefined,
          max: maxPrice ? parseFloat(maxPrice as string) : undefined
        } : undefined,
        amountRange: minAmount || maxAmount ? {
          min: minAmount ? parseFloat(minAmount as string) : undefined,
          max: maxAmount ? parseFloat(maxAmount as string) : undefined
        } : undefined
      })

      res.json({ success: true, data: offers })
    } catch (error) {
      console.error('Error fetching market offers:', error)
      res.status(500).json({ error: 'Failed to fetch market offers' })
    }
  }
)

/**
 * POST /api/market/offers
 * Create a new energy offer
 */
router.post('/offers',
  authenticateWallet,
  rateLimiter,
  [
    body('energyAmount').isFloat({ min: 0.001 }).withMessage('Energy amount must be greater than 0.001'),
    body('pricePerKwh').isFloat({ min: 0.001 }).withMessage('Price per kWh must be greater than 0.001'),
    body('offerType').isIn(['immediate', 'scheduled', 'recurring']).withMessage('Invalid offer type'),
    body('duration').isInt({ min: 1, max: 8760 }).withMessage('Duration must be between 1 and 8760 hours'),
    body('scheduledFor').optional().isISO8601().withMessage('Scheduled time must be valid ISO 8601 date'),
    body('recurringPattern').optional().isObject().withMessage('Recurring pattern must be an object')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { 
        energyAmount, 
        pricePerKwh, 
        offerType, 
        duration, 
        scheduledFor, 
        recurringPattern 
      } = req.body

      const offer = await marketService.createOffer({
        seller: walletAddress,
        energyAmount: parseFloat(energyAmount),
        pricePerKwh: parseFloat(pricePerKwh),
        offerType,
        duration: parseInt(duration),
        scheduledFor: scheduledFor ? new Date(scheduledFor) : undefined,
        recurringPattern
      })

      res.status(201).json({ success: true, data: offer })
    } catch (error) {
      console.error('Error creating offer:', error)
      res.status(500).json({ error: 'Failed to create offer' })
    }
  }
)

/**
 * GET /api/market/offers/user
 * Get user's offers
 */
router.get('/offers/user',
  authenticateWallet,
  [
    query('status').optional().isIn(['active', 'completed', 'cancelled', 'expired']).withMessage('Invalid status'),
    query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
    query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be non-negative')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { status, limit = 50, offset = 0 } = req.query

      const offers = await marketService.getUserOffers(walletAddress, {
        status: status as 'active' | 'completed' | 'cancelled' | 'expired' | undefined,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string)
      })

      res.json({ success: true, data: offers })
    } catch (error) {
      console.error('Error fetching user offers:', error)
      res.status(500).json({ error: 'Failed to fetch user offers' })
    }
  }
)

/**
 * POST /api/market/trade
 * Execute a trade
 */
router.post('/trade',
  authenticateWallet,
  rateLimiter,
  [
    body('offerId').isString().notEmpty().withMessage('Offer ID is required'),
    body('amount').isFloat({ min: 0.001 }).withMessage('Amount must be greater than 0.001'),
    body('maxPrice').optional().isFloat({ min: 0 }).withMessage('Max price must be non-negative')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { offerId, amount, maxPrice } = req.body

      const trade = await marketService.executeTrade({
        buyer: walletAddress,
        offerId,
        amount: parseFloat(amount),
        maxPrice: maxPrice ? parseFloat(maxPrice) : undefined
      })

      res.status(201).json({ success: true, data: trade })
    } catch (error) {
      console.error('Error executing trade:', error)
      res.status(500).json({ error: 'Failed to execute trade' })
    }
  }
)

/**
 * DELETE /api/market/offers/:offerId
 * Cancel an offer
 */
router.delete('/offers/:offerId',
  authenticateWallet,
  async (req, res) => {
    try {
      const walletAddress = req.user?.walletAddress
      if (!walletAddress) {
        return res.status(401).json({ error: 'Wallet address required' })
      }

      const { offerId } = req.params

      await marketService.cancelOffer(offerId, walletAddress)
      res.json({ success: true, message: 'Offer cancelled successfully' })
    } catch (error) {
      console.error('Error cancelling offer:', error)
      res.status(500).json({ error: 'Failed to cancel offer' })
    }
  }
)

/**
 * GET /api/market/stats
 * Get market statistics
 */
router.get('/stats',
  async (req, res) => {
    try {
      const stats = await marketService.getMarketStats()
      res.json({ success: true, data: stats })
    } catch (error) {
      console.error('Error fetching market stats:', error)
      res.status(500).json({ error: 'Failed to fetch market statistics' })
    }
  }
)

/**
 * GET /api/market/price-history
 * Get price history data
 */
router.get('/price-history',
  [
    query('period').optional().isIn(['1h', '24h', '7d', '30d']).withMessage('Invalid period'),
    query('interval').optional().isIn(['1m', '5m', '15m', '1h', '1d']).withMessage('Invalid interval')
  ],
  validateRequest,
  async (req, res) => {
    try {
      const { period = '24h', interval = '1h' } = req.query

      const priceHistory = await marketService.getPriceHistory({
        period: period as '1h' | '24h' | '7d' | '30d',
        interval: interval as '1m' | '5m' | '15m' | '1h' | '1d'
      })

      res.json({ success: true, data: priceHistory })
    } catch (error) {
      console.error('Error fetching price history:', error)
      res.status(500).json({ error: 'Failed to fetch price history' })
    }
  }
)

export default router
